# Pallaidium Quick Installation & Usage Guide

## ✅ Prerequisites Completed
- **Blender 4.5.0 Beta** ✅ Installed and running as Administrator
- **NVIDIA RTX A4500 (20GB VRAM)** ✅ Exceeds 6GB requirement
- **CUDA 12.4** ✅ Perfect match
- **Windows OS** ✅
- **Pallaidium.zip** ✅ Ready at: `C:\Users\<USER>\PycharmProjects\Pallaidium\Pallaidium.zip`

## 🚀 Installation Steps (In Blender)

### Step 1: Install the Add-on
1. **Edit** → **Preferences**
2. Click **Add-ons** (left sidebar)
3. Click **Install...** button
4. Navigate to: `C:\Users\<USER>\PycharmProjects\Pallaidium\Pallaidium.zip`
5. Select the zip file → **Install Add-on**
6. **✅ CHECK** the checkbox next to **"Pallaidium - Generative AI"**

### Step 2: Install Dependencies
1. In Add-ons preferences, find **"Pallaidium - Generative AI"**
2. **Expand** it by clicking the arrow (▼)
3. Click **"Uninstall Dependencies"** first (if button exists)
4. **RESTART Blender as Administrator**
5. Go back to **Add-ons** → **Pallaidium**
6. Click **"Install Dependencies"**
7. **Wait 10-15 minutes** for installation to complete

### Step 3: Final Setup
1. **RESTART your computer** (recommended)
2. **Start Blender as Administrator** again
3. Switch to **Video Sequence Editor** workspace
4. Press **"N"** to open the sidebar
5. Look for **"Generative AI"** tab

## 🎯 How to Use Pallaidium

### Accessing the Interface
- **Workspace**: Video Sequence Editor
- **Sidebar**: Press "N" key
- **Tab**: "Generative AI"

### What You Can Generate
- **Text-to-Video**: Create videos from text descriptions
- **Text-to-Image**: Generate images from prompts
- **Text-to-Audio**: Create music and sound effects
- **Text-to-Speech**: Convert text to spoken audio
- **Image-to-Video**: Animate static images
- **Video-to-Video**: Transform existing videos

### First Use Notes
- **First model download**: 5-10 GB (one-time)
- **Models cached locally** for future use
- **Stable internet required** for initial setup
- **Your RTX A4500** is perfect for this workload

## 🔧 Troubleshooting

### If Installation Fails
- ✅ **Run Blender as Administrator** (critical!)
- ✅ **Check internet connection** for downloads
- ✅ **Restart computer** after dependency installation
- ✅ **Check Blender console** for error messages

### If Add-on Doesn't Appear
- Verify the checkbox is **checked** in Add-ons preferences
- Look in **Video Sequence Editor** workspace (not Default)
- Press **"N"** to ensure sidebar is visible
- Restart Blender if needed

### Performance Tips
- **Close other GPU-intensive applications**
- **Ensure adequate cooling** for long generation sessions
- **Monitor VRAM usage** (you have plenty with 20GB)

## 📁 File Locations
- **Add-on zip**: `C:\Users\<USER>\PycharmProjects\Pallaidium\Pallaidium.zip`
- **Blender executable**: `C:\Program Files\Blender Foundation\Blender 4.5\blender.exe`
- **Models cache**: Will be created in Blender's user directory

## 🎉 You're Ready!
Your system exceeds all requirements. Once dependencies are installed, you'll have a powerful AI video generation tool integrated directly into Blender!

## 📚 Next Steps
1. Complete the installation steps above
2. Explore the Generative AI tab in Video Sequence Editor
3. Try generating your first image or video
4. Experiment with different prompts and settings

---
**Note**: The first generation will take longer as models download. Subsequent generations will be much faster!
