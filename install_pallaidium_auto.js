#!/usr/bin/env node

const { spawn } = require('child_process');

class PallaidiumInstaller {
    constructor() {
        this.mcpProcess = null;
        this.commandId = 1;
        this.blenderWindowId = 460566; // From detection
        this.pallaidiumZipPath = "C:\\Users\\<USER>\\PycharmProjects\\Pallaidium\\Pallaidium.zip";
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    startMCPServer() {
        console.log("🚀 Starting MCP Desktop Pro server...");
        
        this.mcpProcess = spawn('npx', ['mcp-desktop-pro', '--debug'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: process.cwd(),
            shell: true
        });

        this.mcpProcess.stdout.on('data', (data) => {
            console.log(`📤 MCP: ${data.toString().trim()}`);
        });

        this.mcpProcess.stderr.on('data', (data) => {
            console.log(`⚠️ MCP Error: ${data.toString().trim()}`);
        });

        return new Promise((resolve) => {
            setTimeout(() => {
                console.log("✅ MCP server ready");
                resolve();
            }, 3000);
        });
    }

    sendMCPCommand(toolName, args = {}) {
        return new Promise((resolve, reject) => {
            const command = {
                jsonrpc: "2.0",
                id: this.commandId++,
                method: "tools/call",
                params: {
                    name: toolName,
                    arguments: args
                }
            };

            console.log(`📨 ${toolName}: ${JSON.stringify(args)}`);

            const commandStr = JSON.stringify(command) + '\n';
            this.mcpProcess.stdin.write(commandStr);

            const timeout = setTimeout(() => {
                reject(new Error(`${toolName} timed out`));
            }, 15000);

            const onData = (data) => {
                clearTimeout(timeout);
                this.mcpProcess.stdout.removeListener('data', onData);
                
                try {
                    const response = JSON.parse(data.toString());
                    console.log(`✅ ${toolName} completed`);
                    resolve(response);
                } catch (e) {
                    console.log(`📥 ${toolName}: ${data.toString().substring(0, 100)}...`);
                    resolve({ raw: data.toString() });
                }
            };

            this.mcpProcess.stdout.once('data', onData);
        });
    }

    async focusBlender() {
        console.log("🎯 Focusing Blender window...");
        await this.sendMCPCommand('focus_window', { windowId: this.blenderWindowId });
        await this.delay(2000);
    }

    async clickAt(x, y) {
        console.log(`🖱️ Clicking at (${x}, ${y})`);
        await this.sendMCPCommand('mouse_click', {
            x: x,
            y: y,
            windowId: this.blenderWindowId,
            button: 'left'
        });
        await this.delay(1500);
    }

    async pressKey(key, modifiers = []) {
        console.log(`⌨️ Pressing: ${modifiers.length ? modifiers.join('+') + '+' : ''}${key}`);
        const args = { key, windowId: this.blenderWindowId };
        if (modifiers.length > 0) args.modifiers = modifiers;
        
        await this.sendMCPCommand('keyboard_press', args);
        await this.delay(1000);
    }

    async typeText(text) {
        console.log(`⌨️ Typing: "${text}"`);
        await this.sendMCPCommand('keyboard_type', {
            text: text,
            windowId: this.blenderWindowId
        });
        await this.delay(1000);
    }

    async installPallaidium() {
        console.log("🎯 Starting automated Pallaidium installation...");
        
        try {
            // Start MCP server
            await this.startMCPServer();
            
            // Focus Blender
            await this.focusBlender();
            
            console.log("📝 Step 1: Opening Edit menu...");
            await this.clickAt(50, 30); // Edit menu
            await this.delay(1000);
            
            console.log("⚙️ Step 2: Clicking Preferences...");
            await this.clickAt(100, 200); // Preferences
            await this.delay(3000);
            
            console.log("🔌 Step 3: Clicking Add-ons...");
            await this.clickAt(100, 150); // Add-ons in sidebar
            await this.delay(2000);
            
            console.log("📦 Step 4: Clicking Install button...");
            await this.clickAt(800, 100); // Install button (top right area)
            await this.delay(3000);
            
            console.log("📁 Step 5: Navigating to Pallaidium.zip...");
            // Clear any existing path and type new one
            await this.pressKey('a', ['ctrl']); // Select all
            await this.typeText(this.pallaidiumZipPath);
            await this.delay(2000);
            await this.pressKey('Enter');
            await this.delay(5000); // Wait for installation
            
            console.log("✅ Step 6: Enabling Pallaidium add-on...");
            // Search for Pallaidium
            await this.clickAt(400, 80); // Search box
            await this.typeText("Pallaidium");
            await this.delay(2000);
            
            // Click the checkbox to enable
            await this.clickAt(50, 150); // Checkbox area
            await this.delay(3000);
            
            console.log("🔧 Step 7: Installing dependencies...");
            // Expand add-on details
            await this.clickAt(30, 150); // Arrow to expand
            await this.delay(1000);
            
            // Look for Install Dependencies button and click it
            await this.clickAt(200, 200); // Dependencies button area
            await this.delay(2000);
            
            console.log("🎉 Pallaidium installation automation completed!");
            console.log("📋 Next steps:");
            console.log("1. Wait for dependencies to install (10-15 minutes)");
            console.log("2. Restart Blender as Administrator");
            console.log("3. Go to Video Editing workspace");
            console.log("4. Press 'N' to open sidebar");
            console.log("5. Look for 'Generative AI' tab");
            
            return true;
            
        } catch (error) {
            console.error("❌ Installation failed:", error.message);
            return false;
        }
    }

    cleanup() {
        console.log("🧹 Cleaning up...");
        if (this.mcpProcess) {
            this.mcpProcess.kill();
            this.mcpProcess = null;
        }
    }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
    console.log("\n🛑 Received interrupt signal");
    process.exit(0);
});

process.on('exit', () => {
    console.log("👋 Goodbye!");
});

// Main execution
async function main() {
    const installer = new PallaidiumInstaller();
    
    try {
        console.log("🎯 Automated Pallaidium Installation Starting...");
        console.log(`📍 Target Blender Window ID: ${installer.blenderWindowId}`);
        console.log(`📦 Pallaidium Package: ${installer.pallaidiumZipPath}`);
        console.log("");
        
        const success = await installer.installPallaidium();
        
        if (success) {
            console.log("\n🎉 Automation completed successfully!");
        } else {
            console.log("\n❌ Automation failed. Check the logs above.");
        }
        
    } catch (error) {
        console.error("💥 Fatal error:", error.message);
    } finally {
        installer.cleanup();
    }
}

if (require.main === module) {
    main();
}
