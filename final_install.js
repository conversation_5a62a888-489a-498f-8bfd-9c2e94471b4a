#!/usr/bin/env node

const { spawn } = require('child_process');

class FinalInstaller {
    constructor() {
        this.mcpProcess = null;
        this.commandId = 1;
        this.blenderWindowId = 460566; // Known from detection
        this.pallaidiumZipPath = "C:\\Users\\<USER>\\PycharmProjects\\Pallaidium\\Pallaidium.zip";
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    startMCPServer() {
        console.log("🚀 Starting MCP Desktop Pro server...");
        
        this.mcpProcess = spawn('npx', ['mcp-desktop-pro', '--debug'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: process.cwd(),
            shell: true
        });

        this.mcpProcess.stdout.on('data', (data) => {
            // Only log important responses, not all debug output
            const text = data.toString();
            if (text.includes('"success":true') || text.includes('error')) {
                console.log(`📤 MCP: ${text.substring(0, 100)}...`);
            }
        });

        this.mcpProcess.stderr.on('data', (data) => {
            console.log(`⚠️ MCP Error: ${data.toString().trim()}`);
        });

        return new Promise((resolve) => {
            setTimeout(() => {
                console.log("✅ MCP server ready");
                resolve();
            }, 3000);
        });
    }

    sendMCPCommand(toolName, args = {}) {
        return new Promise((resolve, reject) => {
            const command = {
                jsonrpc: "2.0",
                id: this.commandId++,
                method: "tools/call",
                params: {
                    name: toolName,
                    arguments: args
                }
            };

            console.log(`📨 ${toolName}`);

            const commandStr = JSON.stringify(command) + '\n';
            this.mcpProcess.stdin.write(commandStr);

            const timeout = setTimeout(() => {
                reject(new Error(`${toolName} timed out`));
            }, 8000);

            const onData = (data) => {
                clearTimeout(timeout);
                this.mcpProcess.stdout.removeListener('data', onData);
                
                try {
                    const response = JSON.parse(data.toString());
                    console.log(`✅ ${toolName} completed`);
                    resolve(response);
                } catch (e) {
                    console.log(`✅ ${toolName} completed (raw response)`);
                    resolve({ raw: data.toString() });
                }
            };

            this.mcpProcess.stdout.once('data', onData);
        });
    }

    async focusBlender() {
        console.log(`🎯 Focusing Blender window ${this.blenderWindowId}...`);
        await this.sendMCPCommand('focus_window', { windowId: this.blenderWindowId });
        await this.delay(2000);
    }

    async clickAt(x, y) {
        console.log(`🖱️ Clicking at (${x}, ${y})`);
        await this.sendMCPCommand('mouse_click', {
            x: x,
            y: y,
            windowId: this.blenderWindowId,
            button: 'left'
        });
        await this.delay(1500);
    }

    async pressKey(key, modifiers = []) {
        console.log(`⌨️ Pressing: ${modifiers.length ? modifiers.join('+') + '+' : ''}${key}`);
        const args = { key, windowId: this.blenderWindowId };
        if (modifiers.length > 0) args.modifiers = modifiers;
        
        await this.sendMCPCommand('keyboard_press', args);
        await this.delay(1000);
    }

    async typeText(text) {
        console.log(`⌨️ Typing: "${text}"`);
        await this.sendMCPCommand('keyboard_type', {
            text: text,
            windowId: this.blenderWindowId
        });
        await this.delay(1500);
    }

    async installPallaidium() {
        console.log("🎯 Final Pallaidium installation attempt...");
        
        try {
            // Start MCP server
            await this.startMCPServer();
            
            // Focus Blender
            await this.focusBlender();
            
            console.log("📝 Step 1: Opening Edit menu...");
            await this.clickAt(50, 30);
            await this.delay(1000);
            
            console.log("⚙️ Step 2: Clicking Preferences...");
            await this.clickAt(100, 200);
            await this.delay(3000);
            
            console.log("🔌 Step 3: Clicking Add-ons...");
            await this.clickAt(100, 150);
            await this.delay(2000);
            
            console.log("📦 Step 4: Clicking Install button...");
            // Try multiple possible locations for Install button
            const installLocations = [
                [800, 100], [700, 80], [600, 100], [500, 80], [400, 100]
            ];
            
            for (const [x, y] of installLocations) {
                console.log(`🔍 Trying Install button at (${x}, ${y})`);
                await this.clickAt(x, y);
                await this.delay(2000);
                
                // Try to enter the file path
                console.log("📁 Entering file path...");
                await this.pressKey('a', ['control']);
                await this.delay(500);
                await this.typeText(this.pallaidiumZipPath);
                await this.delay(1000);
                await this.pressKey('Enter');
                await this.delay(5000);
                
                // Check if we can search for Pallaidium (indicates successful install)
                console.log("🔍 Searching for Pallaidium...");
                await this.clickAt(400, 80); // Search box
                await this.delay(1000);
                await this.pressKey('a', ['control']); // Clear search
                await this.delay(500);
                await this.typeText("Pallaidium");
                await this.delay(2000);
                
                // Try to enable it
                console.log("✅ Attempting to enable Pallaidium...");
                await this.clickAt(50, 150); // Checkbox area
                await this.delay(2000);
                
                // Try to expand and find Install Dependencies
                console.log("🔧 Looking for Install Dependencies...");
                await this.clickAt(30, 150); // Expand arrow
                await this.delay(1000);
                await this.clickAt(200, 200); // Dependencies button
                await this.delay(2000);
                
                break; // Only try first location
            }
            
            console.log("🎉 Installation sequence completed!");
            console.log("");
            console.log("📋 What should happen next:");
            console.log("1. If Pallaidium appears in add-ons list with ✅ checkbox");
            console.log("2. Dependencies should start installing (10-15 minutes)");
            console.log("3. Restart Blender as Administrator when done");
            console.log("4. Go to Video Editing workspace");
            console.log("5. Press 'N' and look for 'Generative AI' tab");
            console.log("");
            console.log("🔍 Check Blender now to see if Pallaidium is visible in Add-ons!");
            
            return true;
            
        } catch (error) {
            console.error("❌ Installation failed:", error.message);
            return false;
        }
    }

    cleanup() {
        console.log("🧹 Cleaning up...");
        if (this.mcpProcess) {
            this.mcpProcess.kill();
            this.mcpProcess = null;
        }
    }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
    console.log("\n🛑 Received interrupt signal");
    process.exit(0);
});

process.on('exit', () => {
    console.log("👋 Goodbye!");
});

// Main execution
async function main() {
    const installer = new FinalInstaller();
    
    try {
        console.log("🎯 Final Pallaidium Installation");
        console.log(`📍 Blender Window ID: ${installer.blenderWindowId}`);
        console.log(`📦 Package: ${installer.pallaidiumZipPath}`);
        console.log("");
        
        const success = await installer.installPallaidium();
        
        if (success) {
            console.log("\n🎉 Final installation attempt completed!");
            console.log("Check Blender to see if Pallaidium is now visible in Add-ons.");
        } else {
            console.log("\n❌ Final installation attempt failed.");
        }
        
    } catch (error) {
        console.error("💥 Fatal error:", error.message);
    } finally {
        installer.cleanup();
    }
}

if (require.main === module) {
    main();
}
