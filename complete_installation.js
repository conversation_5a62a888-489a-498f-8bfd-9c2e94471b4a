#!/usr/bin/env node

const { spawn } = require('child_process');

class BlenderInstaller {
    constructor() {
        this.mcpProcess = null;
        this.commandId = 1;
        this.pallaidiumZipPath = "C:\\Users\\<USER>\\PycharmProjects\\Pallaidium\\Pallaidium.zip";
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    startMCPServer() {
        console.log("🚀 Starting MCP Desktop Pro server...");
        
        this.mcpProcess = spawn('npx', ['mcp-desktop-pro', '--debug'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: process.cwd(),
            shell: true
        });

        this.mcpProcess.stdout.on('data', (data) => {
            console.log(`📤 MCP: ${data.toString().trim()}`);
        });

        this.mcpProcess.stderr.on('data', (data) => {
            console.log(`⚠️ MCP Error: ${data.toString().trim()}`);
        });

        return new Promise((resolve) => {
            setTimeout(() => {
                console.log("✅ MCP server ready");
                resolve();
            }, 3000);
        });
    }

    sendMCPCommand(toolName, args = {}) {
        return new Promise((resolve, reject) => {
            const command = {
                jsonrpc: "2.0",
                id: this.commandId++,
                method: "tools/call",
                params: {
                    name: toolName,
                    arguments: args
                }
            };

            console.log(`📨 ${toolName}`);

            const commandStr = JSON.stringify(command) + '\n';
            this.mcpProcess.stdin.write(commandStr);

            const timeout = setTimeout(() => {
                reject(new Error(`${toolName} timed out`));
            }, 10000);

            const onData = (data) => {
                clearTimeout(timeout);
                this.mcpProcess.stdout.removeListener('data', onData);
                
                try {
                    const response = JSON.parse(data.toString());
                    console.log(`✅ ${toolName} completed`);
                    resolve(response);
                } catch (e) {
                    console.log(`📥 ${toolName}: ${data.toString().substring(0, 100)}...`);
                    resolve({ raw: data.toString() });
                }
            };

            this.mcpProcess.stdout.once('data', onData);
        });
    }

    async takeScreenshot() {
        console.log("📸 Taking screenshot...");
        return await this.sendMCPCommand('screen_capture');
    }

    async findBlenderWindow() {
        console.log("🔍 Finding Blender window...");
        const windows = await this.sendMCPCommand('list_windows');
        
        // Parse window list and find Blender
        try {
            if (windows.result && windows.result.content) {
                const textContent = windows.result.content.find(c => c.type === 'text');
                if (textContent) {
                    const windowData = JSON.parse(textContent.text);
                    const windowList = windowData.result || [];
                    
                    const blenderWindow = windowList.find(w => 
                        w.title && w.title.toLowerCase().includes('blender')
                    );
                    
                    if (blenderWindow) {
                        console.log(`✅ Found Blender: "${blenderWindow.title}" (ID: ${blenderWindow.id})`);
                        return blenderWindow;
                    }
                }
            }
        } catch (e) {
            console.log("⚠️ Error parsing windows:", e.message);
        }
        
        console.log("❌ Blender window not found");
        return null;
    }

    async focusWindow(windowId) {
        console.log(`🎯 Focusing window ${windowId}...`);
        await this.sendMCPCommand('focus_window', { windowId });
        await this.delay(2000);
    }

    async clickAt(x, y, windowId) {
        console.log(`🖱️ Clicking at (${x}, ${y})`);
        await this.sendMCPCommand('mouse_click', {
            x: x,
            y: y,
            windowId: windowId,
            button: 'left'
        });
        await this.delay(1500);
    }

    async pressKey(key, windowId, modifiers = []) {
        console.log(`⌨️ Pressing: ${modifiers.length ? modifiers.join('+') + '+' : ''}${key}`);
        const args = { key, windowId };
        if (modifiers.length > 0) args.modifiers = modifiers;
        
        await this.sendMCPCommand('keyboard_press', args);
        await this.delay(1000);
    }

    async typeText(text, windowId) {
        console.log(`⌨️ Typing: "${text}"`);
        await this.sendMCPCommand('keyboard_type', {
            text: text,
            windowId: windowId
        });
        await this.delay(1000);
    }

    async completeInstallation() {
        console.log("🎯 Completing Pallaidium installation...");
        
        try {
            // Start MCP server
            await this.startMCPServer();
            
            // Take screenshot to see current state
            await this.takeScreenshot();
            
            // Find Blender window
            const blenderWindow = await this.findBlenderWindow();
            if (!blenderWindow) {
                console.log("❌ Cannot find Blender window");
                return false;
            }
            
            // Focus Blender
            await this.focusWindow(blenderWindow.id);
            
            // Take another screenshot
            await this.takeScreenshot();
            
            console.log("🔧 Attempting to complete installation...");
            
            // Try to open Edit menu again
            console.log("📝 Opening Edit menu...");
            await this.clickAt(50, 30, blenderWindow.id);
            await this.delay(1000);
            
            // Click Preferences
            console.log("⚙️ Clicking Preferences...");
            await this.clickAt(100, 200, blenderWindow.id);
            await this.delay(3000);
            
            // Click Add-ons
            console.log("🔌 Clicking Add-ons...");
            await this.clickAt(100, 150, blenderWindow.id);
            await this.delay(2000);
            
            // Try different locations for Install button
            console.log("📦 Looking for Install button...");
            const installButtonLocations = [
                [800, 100],  // Top right
                [700, 80],   // Alternative top right
                [600, 100],  // Center right
                [500, 80],   // Center
                [400, 100]   // Left center
            ];
            
            for (const [x, y] of installButtonLocations) {
                console.log(`🔍 Trying Install button at (${x}, ${y})`);
                await this.clickAt(x, y, blenderWindow.id);
                await this.delay(3000);
                
                // Check if file dialog opened by trying to type the path
                console.log("📁 Attempting to enter file path...");
                await this.pressKey('a', blenderWindow.id, ['control']);
                await this.delay(500);
                await this.typeText(this.pallaidiumZipPath, blenderWindow.id);
                await this.delay(2000);
                await this.pressKey('Enter', blenderWindow.id);
                await this.delay(5000);
                
                // Check if installation succeeded by looking for Pallaidium in add-ons
                console.log("🔍 Searching for Pallaidium...");
                await this.clickAt(400, 80, blenderWindow.id); // Search box
                await this.delay(1000);
                await this.typeText("Pallaidium", blenderWindow.id);
                await this.delay(2000);
                
                // Try to enable the add-on
                console.log("✅ Attempting to enable Pallaidium...");
                await this.clickAt(50, 150, blenderWindow.id); // Checkbox
                await this.delay(2000);
                
                // Take screenshot to see result
                await this.takeScreenshot();
                
                break; // Try only the first location for now
            }
            
            console.log("🎉 Installation attempt completed!");
            console.log("📋 Next steps:");
            console.log("1. Check if Pallaidium appears in the add-ons list");
            console.log("2. If visible, make sure the checkbox is checked");
            console.log("3. Look for 'Install Dependencies' button");
            console.log("4. Restart Blender as Administrator after dependencies install");
            
            return true;
            
        } catch (error) {
            console.error("❌ Installation failed:", error.message);
            return false;
        }
    }

    cleanup() {
        console.log("🧹 Cleaning up...");
        if (this.mcpProcess) {
            this.mcpProcess.kill();
            this.mcpProcess = null;
        }
    }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
    console.log("\n🛑 Received interrupt signal");
    process.exit(0);
});

process.on('exit', () => {
    console.log("👋 Goodbye!");
});

// Main execution
async function main() {
    const installer = new BlenderInstaller();
    
    try {
        console.log("🎯 Completing Pallaidium Installation...");
        console.log("📦 Target file: C:\\Users\\<USER>\\PycharmProjects\\Pallaidium\\Pallaidium.zip");
        console.log("");
        
        const success = await installer.completeInstallation();
        
        if (success) {
            console.log("\n🎉 Installation completion attempted!");
        } else {
            console.log("\n❌ Installation completion failed.");
        }
        
    } catch (error) {
        console.error("💥 Fatal error:", error.message);
    } finally {
        installer.cleanup();
    }
}

if (require.main === module) {
    main();
}
