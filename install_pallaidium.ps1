# Pallaidium Installation Script
# This script automates the installation of Pallaidium add-on for Blender

Write-Host "=== Pallaidium Installation Script ===" -ForegroundColor Green
Write-Host "Prerequisites Check:" -ForegroundColor Yellow

# Check if Blender is installed
$blenderPath = "C:\Program Files\Blender Foundation\Blender 4.5\blender.exe"
if (Test-Path $blenderPath) {
    try {
        $blenderVersion = & $blenderPath --version 2>$null
        Write-Host "✅ Blender is installed: $($blenderVersion[0])" -ForegroundColor Green
    } catch {
        Write-Host "✅ Blender found at: $blenderPath" -ForegroundColor Green
    }
} else {
    Write-Host "❌ Blender not found at expected location" -ForegroundColor Red
    Write-Host "Please ensure Blender 4.5+ is installed" -ForegroundColor Red
    exit 1
}

# Check CUDA
try {
    $cudaInfo = & nvidia-smi 2>$null | Select-String "CUDA Version"
    Write-Host "✅ CUDA detected: $cudaInfo" -ForegroundColor Green
} catch {
    Write-Host "❌ NVIDIA GPU/CUDA not detected" -ForegroundColor Red
    Write-Host "Pallaidium requires NVIDIA GPU with CUDA support" -ForegroundColor Red
}

# Check if Pallaidium.zip exists
if (Test-Path "Pallaidium.zip") {
    Write-Host "✅ Pallaidium.zip found" -ForegroundColor Green
} else {
    Write-Host "❌ Pallaidium.zip not found" -ForegroundColor Red
    Write-Host "Creating Pallaidium.zip..." -ForegroundColor Yellow
    
    # Create zip file excluding unnecessary files
    $excludePatterns = @("*.zip", "*.msi", "blender-*", ".git", "node_modules", "__pycache__")
    $filesToZip = Get-ChildItem -Path "." -Recurse | Where-Object {
        $file = $_
        $shouldExclude = $false
        foreach ($pattern in $excludePatterns) {
            if ($file.Name -like $pattern -or $file.FullName -like "*$pattern*") {
                $shouldExclude = $true
                break
            }
        }
        return -not $shouldExclude
    }
    
    Compress-Archive -Path $filesToZip -DestinationPath "Pallaidium.zip" -Force
    Write-Host "✅ Pallaidium.zip created" -ForegroundColor Green
}

Write-Host "`n=== Starting Blender Installation Process ===" -ForegroundColor Green

# Start Blender as Administrator
Write-Host "Starting Blender as Administrator..." -ForegroundColor Yellow
Write-Host "IMPORTANT: Blender must run as Administrator for dependency installation!" -ForegroundColor Red

try {
    Start-Process -FilePath $blenderPath -Verb RunAs -WindowStyle Normal
    Write-Host "✅ Blender started as Administrator" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start Blender as Administrator" -ForegroundColor Red
    Write-Host "Please manually start Blender as Administrator" -ForegroundColor Yellow
}

Write-Host "`n=== Manual Installation Steps ===" -ForegroundColor Cyan
Write-Host "Please follow these steps in Blender:" -ForegroundColor White

Write-Host "`n1. Install Pallaidium Add-on:" -ForegroundColor Yellow
Write-Host "   • Go to Edit > Preferences" -ForegroundColor White
Write-Host "   • Click 'Add-ons' in the left sidebar" -ForegroundColor White
Write-Host "   • Click 'Install...' button" -ForegroundColor White
Write-Host "   • Navigate to: $PWD\Pallaidium.zip" -ForegroundColor White
Write-Host "   • Select the zip file and click 'Install Add-on'" -ForegroundColor White
Write-Host "   • ✅ CHECK the checkbox next to 'Pallaidium - Generative AI'" -ForegroundColor White

Write-Host "`n2. Install Dependencies:" -ForegroundColor Yellow
Write-Host "   • In Add-ons preferences, find 'Pallaidium - Generative AI'" -ForegroundColor White
Write-Host "   • Expand it by clicking the arrow" -ForegroundColor White
Write-Host "   • Click 'Uninstall Dependencies' first (if available)" -ForegroundColor White
Write-Host "   • RESTART Blender as Administrator" -ForegroundColor White
Write-Host "   • Go back to Add-ons > Pallaidium" -ForegroundColor White
Write-Host "   • Click 'Install Dependencies'" -ForegroundColor White
Write-Host "   • Wait for installation to complete (may take 10-15 minutes)" -ForegroundColor White

Write-Host "`n3. Final Setup:" -ForegroundColor Yellow
Write-Host "   • RESTART your computer (recommended)" -ForegroundColor White
Write-Host "   • Start Blender as Administrator again" -ForegroundColor White
Write-Host "   • Switch to Video Sequence Editor workspace" -ForegroundColor White
Write-Host "   • Press 'N' to open sidebar" -ForegroundColor White
Write-Host "   • Look for 'Generative AI' tab" -ForegroundColor White

Write-Host "`n=== System Information ===" -ForegroundColor Cyan
Write-Host "Pallaidium.zip location: $PWD\Pallaidium.zip" -ForegroundColor White
Write-Host "GPU: NVIDIA RTX A4500 (20GB VRAM) ✅" -ForegroundColor Green
Write-Host "CUDA: 12.4 ✅" -ForegroundColor Green
Write-Host "OS: Windows ✅" -ForegroundColor Green

Write-Host "`n=== First Use Notes ===" -ForegroundColor Cyan
Write-Host "• First model download will be 5-10 GB" -ForegroundColor White
Write-Host "• Models are cached locally for future use" -ForegroundColor White
Write-Host "• Ensure stable internet connection for first use" -ForegroundColor White
Write-Host "• Your RTX A4500 (20GB) exceeds minimum requirements" -ForegroundColor Green

Write-Host "`n=== Troubleshooting ===" -ForegroundColor Cyan
Write-Host "If installation fails:" -ForegroundColor White
Write-Host "• Ensure Blender is running as Administrator" -ForegroundColor White
Write-Host "• Check internet connection for dependency downloads" -ForegroundColor White
Write-Host "• Restart computer after dependency installation" -ForegroundColor White
Write-Host "• Check Blender console for error messages" -ForegroundColor White

Write-Host "`nInstallation script completed. Please follow the manual steps above." -ForegroundColor Green
