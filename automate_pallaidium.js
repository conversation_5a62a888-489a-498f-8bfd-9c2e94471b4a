#!/usr/bin/env node

const { spawn } = require('child_process');
const readline = require('readline');

class PallaidiumAutomator {
    constructor() {
        this.mcpProcess = null;
        this.commandId = 1;
        this.pallaidiumZipPath = "C:\\Users\\<USER>\\PycharmProjects\\Pallaidium\\Pallaidium.zip";
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    startMCPServer() {
        console.log("🚀 Starting MCP Desktop Pro server...");

        // Try different ways to find npx
        const npxPaths = [
            'npx',
            'npx.cmd',
            'C:\\Program Files\\nodejs\\npx.cmd',
            'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\npx.cmd'
        ];

        let npxPath = 'npx';

        this.mcpProcess = spawn(npxPath, ['mcp-desktop-pro', '--debug'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: process.cwd(),
            shell: true  // Use shell to resolve npx
        });

        this.mcpProcess.stdout.on('data', (data) => {
            console.log(`📤 MCP Output: ${data.toString().trim()}`);
        });

        this.mcpProcess.stderr.on('data', (data) => {
            console.log(`⚠️ MCP Error: ${data.toString().trim()}`);
        });

        this.mcpProcess.on('close', (code) => {
            console.log(`🛑 MCP process exited with code ${code}`);
        });

        return new Promise((resolve) => {
            setTimeout(() => {
                console.log("✅ MCP server should be ready");
                resolve();
            }, 3000);
        });
    }

    sendMCPCommand(toolName, args = {}) {
        return new Promise((resolve, reject) => {
            const command = {
                jsonrpc: "2.0",
                id: this.commandId++,
                method: "tools/call",
                params: {
                    name: toolName,
                    arguments: args
                }
            };

            console.log(`📨 Sending command: ${toolName}`);
            console.log(`📋 Arguments:`, JSON.stringify(args, null, 2));

            const commandStr = JSON.stringify(command) + '\n';
            this.mcpProcess.stdin.write(commandStr);

            // Set up timeout
            const timeout = setTimeout(() => {
                reject(new Error(`Command ${toolName} timed out`));
            }, 15000);

            // Listen for response
            const onData = (data) => {
                clearTimeout(timeout);
                this.mcpProcess.stdout.removeListener('data', onData);
                
                try {
                    const response = JSON.parse(data.toString());
                    console.log(`📥 Response for ${toolName}:`, JSON.stringify(response, null, 2));
                    resolve(response);
                } catch (e) {
                    console.log(`📥 Raw response for ${toolName}: ${data.toString()}`);
                    resolve({ raw: data.toString() });
                }
            };

            this.mcpProcess.stdout.once('data', onData);
        });
    }

    async takeScreenshot() {
        console.log("📸 Taking screenshot...");
        try {
            const result = await this.sendMCPCommand('screen_capture');
            return result;
        } catch (error) {
            console.error("❌ Screenshot failed:", error.message);
            return null;
        }
    }

    async listWindows() {
        console.log("🔍 Listing windows...");
        try {
            const result = await this.sendMCPCommand('list_windows');
            return result;
        } catch (error) {
            console.error("❌ List windows failed:", error.message);
            return null;
        }
    }

    async focusWindow(windowId) {
        console.log(`🎯 Focusing window ID: ${windowId}`);
        try {
            const result = await this.sendMCPCommand('focus_window', { windowId });
            await this.delay(2000); // Wait for window to focus
            return result;
        } catch (error) {
            console.error("❌ Focus window failed:", error.message);
            return null;
        }
    }

    async clickAt(x, y, windowId) {
        console.log(`🖱️ Clicking at (${x}, ${y}) in window ${windowId}`);
        try {
            const result = await this.sendMCPCommand('mouse_click', {
                x: x,
                y: y,
                windowId: windowId,
                button: 'left'
            });
            await this.delay(1000);
            return result;
        } catch (error) {
            console.error("❌ Click failed:", error.message);
            return null;
        }
    }

    async pressKey(key, windowId, modifiers = []) {
        console.log(`⌨️ Pressing key: ${key} in window ${windowId}`);
        try {
            const args = { key, windowId };
            if (modifiers.length > 0) {
                args.modifiers = modifiers;
            }
            
            const result = await this.sendMCPCommand('keyboard_press', args);
            await this.delay(1000);
            return result;
        } catch (error) {
            console.error("❌ Key press failed:", error.message);
            return null;
        }
    }

    async typeText(text, windowId) {
        console.log(`⌨️ Typing: "${text}" in window ${windowId}`);
        try {
            const result = await this.sendMCPCommand('keyboard_type', {
                text: text,
                windowId: windowId
            });
            await this.delay(1000);
            return result;
        } catch (error) {
            console.error("❌ Type text failed:", error.message);
            return null;
        }
    }

    async findBlenderWindow() {
        const windows = await this.listWindows();

        if (!windows || !windows.result || !windows.result.content) {
            console.log("❌ No windows found");
            return null;
        }

        // Parse the window list from the MCP response
        let windowList = [];
        try {
            const textContent = windows.result.content.find(c => c.type === 'text');
            if (textContent) {
                const windowData = JSON.parse(textContent.text);
                windowList = windowData.result || [];
            }
        } catch (e) {
            console.log("⚠️ Error parsing window data:", e.message);
            return null;
        }

        // Look for Blender window
        const blenderWindow = windowList.find(w =>
            w.title && w.title.toLowerCase().includes('blender')
        );

        if (blenderWindow) {
            console.log(`✅ Found Blender window: "${blenderWindow.title}" (ID: ${blenderWindow.id})`);
            console.log(`📍 Location: ${blenderWindow.displayLocation || 'unknown'}`);
            console.log(`📐 Bounds: ${JSON.stringify(blenderWindow.bounds)}`);
            return blenderWindow;
        } else {
            console.log("❌ Blender window not found");
            console.log("Available windows:");
            windowList.forEach(w => {
                console.log(`  - "${w.title}" (ID: ${w.id}) - ${w.displayLocation || 'unknown'}`);
            });
            return null;
        }
    }

    async moveWindowToPrimary(windowId) {
        console.log(`🔄 Moving window ${windowId} to primary display...`);
        try {
            const result = await this.sendMCPCommand('move_window_to_primary_screen', {
                windowId: windowId,
                preserveSize: true
            });
            await this.delay(2000); // Wait for window to move
            return result;
        } catch (error) {
            console.error("❌ Move window failed:", error.message);
            return null;
        }
    }

    async automateBlenderInstallation() {
        console.log("🎯 Starting Blender Pallaidium installation automation...");

        try {
            // Start MCP server
            await this.startMCPServer();

            // Take initial screenshot
            await this.takeScreenshot();

            // Find Blender window
            const blenderWindow = await this.findBlenderWindow();
            if (!blenderWindow) {
                console.log("❌ Cannot proceed without Blender window");
                return false;
            }

            // Move Blender to primary display if needed
            if (blenderWindow.displayLocation && blenderWindow.displayLocation !== 'primary') {
                console.log("🔄 Blender is on secondary display, moving to primary...");
                await this.moveWindowToPrimary(blenderWindow.id);
            }

            // Focus Blender window
            await this.focusWindow(blenderWindow.id);

            // Take screenshot of focused Blender
            await this.takeScreenshot();

            console.log("✅ Blender window is focused and ready!");
            console.log("🎯 Starting automated Pallaidium installation...");

            // Start the actual automation sequence
            await this.performPallaidiumInstallation(blenderWindow.id);

            return true;

        } catch (error) {
            console.error("❌ Automation failed:", error.message);
            return false;
        }
    }

    async performPallaidiumInstallation(windowId) {
        console.log("🚀 Starting Pallaidium installation sequence...");

        try {
            // Step 1: Open Edit menu
            console.log("📝 Step 1: Opening Edit menu...");
            await this.clickAt(50, 30, windowId); // Approximate Edit menu location
            await this.delay(1000);

            // Step 2: Click Preferences
            console.log("⚙️ Step 2: Clicking Preferences...");
            await this.clickAt(100, 200, windowId); // Approximate Preferences location
            await this.delay(2000);

            // Step 3: Click Add-ons
            console.log("🔌 Step 3: Clicking Add-ons...");
            await this.clickAt(100, 150, windowId); // Approximate Add-ons location
            await this.delay(1000);

            // Step 4: Click Install button
            console.log("📦 Step 4: Clicking Install button...");
            await this.clickAt(200, 100, windowId); // Approximate Install button location
            await this.delay(2000);

            // Step 5: Navigate to Pallaidium.zip
            console.log("📁 Step 5: Navigating to Pallaidium.zip...");
            await this.typeText(this.pallaidiumZipPath, windowId);
            await this.delay(1000);
            await this.pressKey('enter', windowId);
            await this.delay(3000);

            // Step 6: Enable the add-on
            console.log("✅ Step 6: Enabling Pallaidium add-on...");
            await this.clickAt(300, 200, windowId); // Approximate checkbox location
            await this.delay(2000);

            console.log("🎉 Pallaidium installation automation completed!");
            console.log("📋 Next steps:");
            console.log("1. Install dependencies by clicking 'Install Dependencies'");
            console.log("2. Restart Blender as Administrator");
            console.log("3. Switch to Video Sequence Editor workspace");
            console.log("4. Press 'N' to open sidebar and look for 'Generative AI' tab");

        } catch (error) {
            console.error("❌ Installation automation failed:", error.message);
        }
    }

    cleanup() {
        console.log("🧹 Cleaning up...");
        if (this.mcpProcess) {
            this.mcpProcess.kill();
            this.mcpProcess = null;
        }
    }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
    console.log("\n🛑 Received interrupt signal");
    process.exit(0);
});

process.on('exit', () => {
    console.log("👋 Goodbye!");
});

// Main execution
async function main() {
    const automator = new PallaidiumAutomator();
    
    try {
        await automator.automateBlenderInstallation();
    } catch (error) {
        console.error("💥 Fatal error:", error.message);
    } finally {
        automator.cleanup();
    }
}

if (require.main === module) {
    main();
}
