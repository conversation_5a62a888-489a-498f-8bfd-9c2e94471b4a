# Blender Automation Script using MCP Desktop Pro
param(
    [string]$Action = "install"
)

Write-Host "=== Blender Pallaidium Automation ===" -ForegroundColor Green

# Check if MCP Desktop Pro is available
try {
    $mcpTest = npx mcp-desktop-pro --help 2>$null
    Write-Host "✅ MCP Desktop Pro is available" -ForegroundColor Green
} catch {
    Write-Host "❌ MCP Desktop Pro not found" -ForegroundColor Red
    exit 1
}

# Global MCP process for persistent connection
$global:mcpProcess = $null

function Start-MCPServer {
    Write-Host "Starting MCP Desktop Pro server..." -ForegroundColor Yellow
    
    $psi = New-Object System.Diagnostics.ProcessStartInfo
    $psi.FileName = "npx"
    $psi.Arguments = "mcp-desktop-pro --debug"
    $psi.UseShellExecute = $false
    $psi.RedirectStandardInput = $true
    $psi.RedirectStandardOutput = $true
    $psi.RedirectStandardError = $true
    $psi.CreateNoWindow = $false
    $psi.WorkingDirectory = $PWD

    $global:mcpProcess = [System.Diagnostics.Process]::Start($psi)
    Start-Sleep -Seconds 3  # Wait for server to start
    
    Write-Host "✅ MCP Server started" -ForegroundColor Green
}

function Send-MCPCommand {
    param(
        [string]$Command,
        [hashtable]$Arguments = @{}
    )
    
    if (-not $global:mcpProcess) {
        Start-MCPServer
    }
    
    $mcpCommand = @{
        jsonrpc = "2.0"
        id = Get-Random
        method = "tools/call"
        params = @{
            name = $Command
            arguments = $Arguments
        }
    } | ConvertTo-Json -Depth 10 -Compress

    Write-Host "Sending MCP command: $Command" -ForegroundColor Yellow
    
    try {
        $global:mcpProcess.StandardInput.WriteLine($mcpCommand)
        $global:mcpProcess.StandardInput.Flush()
        
        # Wait for response with timeout
        $timeout = 10000  # 10 seconds
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        
        while ($stopwatch.ElapsedMilliseconds -lt $timeout) {
            if ($global:mcpProcess.StandardOutput.Peek() -ne -1) {
                $output = $global:mcpProcess.StandardOutput.ReadLine()
                Write-Host "MCP Response: $output" -ForegroundColor Cyan
                return $output
            }
            Start-Sleep -Milliseconds 100
        }
        
        Write-Host "⚠️ MCP command timeout" -ForegroundColor Yellow
        return $null
        
    } catch {
        Write-Host "❌ MCP command failed: $_" -ForegroundColor Red
        return $null
    }
}

function Take-Screenshot {
    Write-Host "📸 Taking screenshot..." -ForegroundColor Yellow
    $result = Send-MCPCommand -Command "screen_capture"
    return $result
}

function Get-WindowList {
    Write-Host "🔍 Getting window list..." -ForegroundColor Yellow
    $result = Send-MCPCommand -Command "list_windows"
    return $result
}

function Focus-BlenderWindow {
    Write-Host "🎯 Looking for Blender window..." -ForegroundColor Yellow
    
    $windows = Get-WindowList
    
    if ($windows) {
        try {
            $windowData = $windows | ConvertFrom-Json
            if ($windowData.content) {
                $blenderWindow = $windowData.content | Where-Object { $_.title -like "*Blender*" }
                
                if ($blenderWindow) {
                    Write-Host "✅ Found Blender window: $($blenderWindow.title)" -ForegroundColor Green
                    
                    $focusResult = Send-MCPCommand -Command "focus_window" -Arguments @{
                        windowId = $blenderWindow.id
                    }
                    
                    Start-Sleep -Seconds 2
                    return $blenderWindow
                }
            }
        } catch {
            Write-Host "⚠️ Error parsing window list: $_" -ForegroundColor Yellow
        }
    }
    
    Write-Host "❌ Blender window not found" -ForegroundColor Red
    return $null
}

function Click-AtPosition {
    param(
        [int]$X,
        [int]$Y,
        [int]$WindowId
    )
    
    Write-Host "🖱️ Clicking at position ($X, $Y)" -ForegroundColor Yellow
    
    $clickResult = Send-MCPCommand -Command "mouse_click" -Arguments @{
        x = $X
        y = $Y
        windowId = $WindowId
        button = "left"
    }
    
    Start-Sleep -Seconds 1
    return $clickResult
}

function Press-Key {
    param(
        [string]$Key,
        [int]$WindowId,
        [string[]]$Modifiers = @()
    )
    
    Write-Host "⌨️ Pressing key: $Key" -ForegroundColor Yellow
    
    $keyArgs = @{
        key = $Key
        windowId = $WindowId
    }
    
    if ($Modifiers.Count -gt 0) {
        $keyArgs.modifiers = $Modifiers
    }
    
    $keyResult = Send-MCPCommand -Command "keyboard_press" -Arguments $keyArgs
    Start-Sleep -Seconds 1
    return $keyResult
}

function Start-BlenderAutomation {
    Write-Host "🚀 Starting Blender automation..." -ForegroundColor Green
    
    # Start MCP server
    Start-MCPServer
    
    # Take initial screenshot
    Take-Screenshot
    
    # Find and focus Blender window
    $blenderWindow = Focus-BlenderWindow
    
    if (-not $blenderWindow) {
        Write-Host "❌ Could not find Blender window. Please ensure Blender is running." -ForegroundColor Red
        return $false
    }
    
    Write-Host "✅ Blender window focused. Taking screenshot..." -ForegroundColor Green
    Take-Screenshot
    
    Write-Host "🎯 Ready for Pallaidium installation automation!" -ForegroundColor Green
    Write-Host "Window ID: $($blenderWindow.id)" -ForegroundColor Cyan
    
    return $blenderWindow
}

function Stop-MCPServer {
    if ($global:mcpProcess) {
        Write-Host "🛑 Stopping MCP server..." -ForegroundColor Yellow
        $global:mcpProcess.Kill()
        $global:mcpProcess = $null
    }
}

# Cleanup on exit
Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Stop-MCPServer
}

# Execute based on action parameter
switch ($Action) {
    "install" {
        $blenderWindow = Start-BlenderAutomation
        if ($blenderWindow) {
            Write-Host "✅ Automation setup complete. Blender window ready for interaction." -ForegroundColor Green
            Write-Host "Press any key to stop MCP server..." -ForegroundColor Yellow
            $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        }
        Stop-MCPServer
    }
    "screenshot" {
        Start-MCPServer
        Take-Screenshot
        Stop-MCPServer
    }
    "windows" {
        Start-MCPServer
        Get-WindowList
        Stop-MCPServer
    }
    default {
        Write-Host "Usage: .\blender_automation.ps1 -Action [install|screenshot|windows]" -ForegroundColor Yellow
    }
}

Write-Host "🏁 Automation script completed." -ForegroundColor Green
